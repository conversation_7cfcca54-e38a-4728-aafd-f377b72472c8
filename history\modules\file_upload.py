#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件上传功能
"""

import requests
import tempfile
import os
import time

class FileUploader:
    """文件上传器"""
    
    def __init__(self):
        self.api_url = "https://api.gptgod.online/v1/file"
        self.api_key = "sk-nuNeNMoNBhlaruvhwokxmhgvEymMq7LyrWtpSTNBdfclGf7m"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}"
        }
    
    def upload_text_file(self, content, filename=None, content_type=None):
        """上传文本内容为文件"""
        try:
            # 生成文件名
            if not filename:
                timestamp = int(time.time())
                filename = f"chat_history_{timestamp}.txt"

            # 根据文件扩展名确定 MIME 类型
            if content_type is None:
                if filename.endswith('.html'):
                    content_type = 'text/html'
                    suffix = '.html'
                elif filename.endswith('.txt'):
                    content_type = 'text/plain'
                    suffix = '.txt'
                else:
                    # 默认为文本文件
                    content_type = 'text/plain'
                    suffix = '.txt'
                    if not filename.endswith('.txt'):
                        filename += '.txt'
            else:
                suffix = '.html' if content_type == 'text/html' else '.txt'

            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8', suffix=suffix, delete=False) as temp_file:
                temp_file.write(content)
                temp_file_path = temp_file.name

            try:
                # 上传文件
                with open(temp_file_path, 'rb') as file:
                    files = {
                        'file': (filename, file, content_type)
                    }

                    response = requests.post(
                        self.api_url,
                        headers=self.headers,
                        files=files,
                        timeout=30
                    )
                
                # 处理响应
                if response.status_code == 200:
                    result = response.json()
                    if result.get('code') == 0:
                        return True, result['data']['url']
                    else:
                        return False, f"上传失败: {result.get('msg', '未知错误')}"
                else:
                    return False, f"HTTP错误: {response.status_code}"
                    
            finally:
                # 清理临时文件
                try:
                    os.unlink(temp_file_path)
                except:
                    pass
                    
        except Exception as e:
            return False, f"上传异常: {str(e)}"
    
# 全局文件上传器实例
file_uploader = FileUploader()
