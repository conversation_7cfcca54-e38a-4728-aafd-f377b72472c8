#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Wordle 图片生成器
"""

from PIL import Image, ImageDraw, ImageFont
import requests
import io
import os
import tempfile

class WordleImageGenerator:
    """Wordle 图片生成器"""
    
    def __init__(self):
        # 颜色配置
        self.colors = {
            'correct': '#6aaa64',    # 绿色 - 正确位置
            'present': '#c9b458',    # 黄色 - 存在但位置错误
            'absent': '#787c7e',     # 灰色 - 不存在
            'empty': '#ffffff',      # 白色 - 空格
            'border': '#d3d6da',     # 边框颜色
            'text': '#000000'        # 文字颜色
        }
        
        # 尺寸配置
        self.cell_size = 60
        self.cell_margin = 5
        self.border_width = 2
        self.font_size = 32
        
        # Gyazo API 配置
        self.gyazo_config = {
            'client_id': '*******************************************',
            'client_secret': 'S-2_CITxALMHLhU_n5N03f4fC68AW7WASHNvm76cGe0',
            'access_token': 'i11XvrHAoAuZ4CwbCAnQfiWLFNOGsXsAUXxfT-Kg6co'
        }
    
    def create_wordle_image(self, wordle_game):
        """创建 Wordle 游戏状态图片"""
        # 计算图片尺寸
        grid_width = 5 * (self.cell_size + self.cell_margin) - self.cell_margin
        grid_height = 6 * (self.cell_size + self.cell_margin) - self.cell_margin

        # 添加边距（不需要标题空间）
        margin = 20
        img_width = grid_width + 2 * margin
        img_height = grid_height + 2 * margin

        # 创建图片
        img = Image.new('RGB', (img_width, img_height), '#ffffff')
        draw = ImageDraw.Draw(img)

        # 尝试加载字体
        try:
            # Windows 系统字体
            font_cell = ImageFont.truetype("arial.ttf", self.font_size)
        except:
            try:
                # Linux 系统字体
                font_cell = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", self.font_size)
            except:
                # 默认字体
                font_cell = ImageFont.load_default()

        # 绘制网格
        start_x = margin
        start_y = margin
        
        for row in range(6):
            for col in range(5):
                # 计算单元格位置
                x = start_x + col * (self.cell_size + self.cell_margin)
                y = start_y + row * (self.cell_size + self.cell_margin)
                
                # 确定单元格内容和颜色
                if row < len(wordle_game.guesses):
                    # 已猜测的行
                    guess_data = wordle_game.guesses[row]
                    letter = guess_data["word"][col]
                    result = guess_data["result"][col]
                    
                    if result == "correct":
                        bg_color = self.colors['correct']
                        text_color = '#ffffff'
                    elif result == "present":
                        bg_color = self.colors['present']
                        text_color = '#ffffff'
                    else:  # absent
                        bg_color = self.colors['absent']
                        text_color = '#ffffff'
                else:
                    # 空行
                    letter = ""
                    bg_color = self.colors['empty']
                    text_color = self.colors['text']
                
                # 绘制单元格背景
                draw.rectangle(
                    [x, y, x + self.cell_size, y + self.cell_size],
                    fill=bg_color,
                    outline=self.colors['border'],
                    width=self.border_width
                )
                
                # 绘制字母
                if letter:
                    # 计算文字居中位置
                    text_bbox = draw.textbbox((0, 0), letter, font=font_cell)
                    text_width = text_bbox[2] - text_bbox[0]
                    text_height = text_bbox[3] - text_bbox[1]
                    text_x = x + (self.cell_size - text_width) // 2
                    text_y = y + (self.cell_size - text_height) // 2
                    
                    draw.text((text_x, text_y), letter, fill=text_color, font=font_cell)
        
        return img
    
    def upload_to_gyazo(self, image):
        """上传图片到 Gyazo"""
        try:
            # 将图片转换为字节流
            img_buffer = io.BytesIO()
            image.save(img_buffer, format='PNG')
            img_buffer.seek(0)
            
            # 准备上传数据
            files = {
                'imagedata': ('wordle.png', img_buffer, 'image/png')
            }
            
            headers = {
                'Authorization': f'Bearer {self.gyazo_config["access_token"]}'
            }
            
            # 上传到 Gyazo
            response = requests.post(
                'https://upload.gyazo.com/api/upload',
                files=files,
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get('url', '')
            else:
                print(f"Gyazo 上传失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"上传图片到 Gyazo 失败: {e}")
            return None
    
    def save_local_backup(self, image, filename="wordle_backup.png"):
        """保存本地备份"""
        try:
            # 确保 temp 目录存在
            temp_dir = "temp"
            os.makedirs(temp_dir, exist_ok=True)
            
            filepath = os.path.join(temp_dir, filename)
            image.save(filepath)
            return filepath
        except Exception as e:
            print(f"保存本地备份失败: {e}")
            return None
    
    def generate_and_upload(self, wordle_game):
        """生成图片并上传，返回图片 URL"""
        try:
            # 生成图片
            image = self.create_wordle_image(wordle_game)
            
            # 保存本地备份
            local_path = self.save_local_backup(image)
            
            # 上传到 Gyazo
            url = self.upload_to_gyazo(image)
            
            if url:
                return url, local_path
            else:
                return None, local_path
                
        except Exception as e:
            print(f"生成和上传图片失败: {e}")
            return None, None

# 全局图片生成器实例
wordle_image_generator = WordleImageGenerator()

def generate_wordle_image(wordle_game):
    """生成 Wordle 图片的便捷函数"""
    return wordle_image_generator.generate_and_upload(wordle_game)
