# HackChat Trip 收集器

这是一个独立的脚本，用于收集 HackChat 中的特殊 trip 值。

## 功能说明

HackChat 的 trip 是根据用户昵称中 `#` 后面的部分生成的唯一标识符。这个脚本会：

1. 自动生成随机昵称 `TripBot_XXXX`
2. 连接到 HackChat 测试房间
3. 获取生成的 trip 值
4. 立即退出房间
5. 判断 trip 是否符合"好 trip"标准
6. 将结果保存到文件中

## 好 Trip 标准

脚本会识别以下类型的"好 trip"：

- **连续字符**: AAA, BBB, 111, 222 等
- **连续递增**: ABC, 123, DEF, 456 等  
- **连续递减**: CBA, 321, FED, 654 等
- **包含特殊单词**: BOB, AI, GPT, BOT, GOD, VIP, PRO, MAX, WIN, TOP
- **回文**: ABA, 121, ABCBA 等
- **重复模式**: ABAB, 1212, CDCD 等
- **全数字**: 12345, 67890 等（至少5位）
- **全字母**: ABCDE, FGHIJ 等（至少5位）

## 使用方法

1. 确保已安装依赖：
   ```bash
   pip install websocket-client
   ```

2. 运行脚本：
   ```bash
   python trip_collector.py
   ```

3. 输入要尝试的次数（默认1000次）

4. 等待收集完成或按 `Ctrl+C` 停止

## 输出文件

- `good_trips.txt`: 包含所有符合标准的好 trip
- `get_trips.txt`: 包含所有收集到的 trip（包括普通的）

## 文件格式

```
时间戳 | 昵称 -> trip (原因)
2024-01-01 12:00:00 | TripBot_A1B2 -> ABC123 (连续递增: ABC)
```

## 注意事项

- 脚本使用测试房间，不会打扰其他用户
- 每次尝试之间有0.5秒延迟，避免请求过快
- 可以随时按 `Ctrl+C` 停止收集
- 所有数据都会实时保存到文件中

## 统计信息

脚本运行结束后会显示：
- 总收集次数
- 好 trip 数量
- 好 trip 比例

## 示例输出

```
尝试 1/1000 - 已收集: 1, 好 trip: 0
🎉 发现好 trip: TripBot_X1Y2 -> BOB456 (包含单词: BOB)
尝试 2/1000 - 已收集: 2, 好 trip: 1
...
```
