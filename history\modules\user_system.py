#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户系统管理
"""

import json
import os

class UserSystem:
    """用户系统管理器"""
    
    def __init__(self, config_file="user_config.json"):
        self.config_file = config_file
        self.default_config = {
            "known_users": [],
            "welcome_message": "欢迎来到聊天室！输入 help 查看机器人功能。"
        }
        self.config = self.load_config()
    
    def load_config(self):
        """加载用户配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 确保所有必要的键都存在
                    for key, value in self.default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
            else:
                return self.default_config.copy()
        except Exception as e:
            print(f"加载用户配置失败: {e}")
            return self.default_config.copy()
    
    def save_config(self):
        """保存用户配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存用户配置失败: {e}")
            return False
    
    def is_known_user(self, username):
        """检查是否为已知用户"""
        return username in self.config["known_users"]
    
    def add_user(self, username):
        """添加新用户"""
        if username not in self.config["known_users"]:
            self.config["known_users"].append(username)
            self.save_config()
            return True
        return False
    
    def get_welcome_message(self):
        """获取欢迎消息"""
        return self.config["welcome_message"]
    
    def set_welcome_message(self, message):
        """设置欢迎消息"""
        self.config["welcome_message"] = message
        return self.save_config()
    
    def get_user_count(self):
        """获取已知用户数量"""
        return len(self.config["known_users"])
    
    def get_user_list(self):
        """获取用户列表"""
        return self.config["known_users"].copy()
    
    def remove_user(self, username):
        """移除用户"""
        if username in self.config["known_users"]:
            self.config["known_users"].remove(username)
            self.save_config()
            return True
        return False
    
    def migrate_from_old_files(self, userhi_file="userhi.txt", wel_file="wel.txt"):
        """从旧文件迁移数据"""
        migrated = False
        
        # 迁移用户列表
        try:
            if os.path.exists(userhi_file):
                with open(userhi_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if content:
                        old_users = content.split(',')
                        for user in old_users:
                            user = user.strip()
                            if user and user not in self.config["known_users"]:
                                self.config["known_users"].append(user)
                                migrated = True
        except Exception as e:
            print(f"迁移用户列表失败: {e}")
        
        # 迁移欢迎消息
        try:
            if os.path.exists(wel_file):
                with open(wel_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if content and content != self.config["welcome_message"]:
                        self.config["welcome_message"] = content
                        migrated = True
        except Exception as e:
            print(f"迁移欢迎消息失败: {e}")
        
        if migrated:
            self.save_config()
            print("✅ 已从旧文件迁移数据到用户系统")
        
        return migrated

# 全局用户系统实例
user_system = UserSystem()

def initialize_user_system():
    """初始化用户系统"""
    # 尝试从旧文件迁移
    user_system.migrate_from_old_files()
    return user_system
