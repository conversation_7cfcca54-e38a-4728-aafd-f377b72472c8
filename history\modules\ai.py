import sys
import json
import os

# 检查 OpenAI 版本并使用相应的导入
try:
    # 尝试新版本 OpenAI (1.0.0+)
    from openai import OpenAI
    OPENAI_NEW_VERSION = True

    # 创建客户端
    client = OpenAI(
        api_key="sk-nuNeNMoNBhlaruvhwokxmhgvEymMq7LyrWtpSTNBdfclGf7m",
        base_url="https://api.gptgod.online/v1"
    )
except ImportError:
    # 回退到旧版本 OpenAI (0.28.x)
    import openai
    OPENAI_NEW_VERSION = False

    # 旧版本配置
    openai.api_key = "sk-nuNeNMoNBhlaruvhwokxmhgvEymMq7LyrWtpSTNBdfclGf7m"
    openai.api_base = "https://api.gptgod.online/v1"

# 模型配置
MODELS = {
    "ai": "gpt-4o-mini",
    "draw": "flux"
}

# 配置
MAX_HISTORY_LENGTH = 50  # 最大历史记录条数
ENCODING = 'utf-8'

def load_chat_history(file_path):
    """安全加载聊天历史"""
    try:
        with open(file_path, 'r', encoding=ENCODING) as f:
            history = json.load(f)
            # 确保历史记录不会过长
            if len(history) > MAX_HISTORY_LENGTH:
                # 保留最近的对话，但确保成对出现
                recent_history = history[-MAX_HISTORY_LENGTH:]
                # 如果第一条是 assistant 消息，删除它以保持对话完整性
                if recent_history and recent_history[0].get('role') == 'assistant':
                    recent_history = recent_history[1:]
                return recent_history
            return history
    except FileNotFoundError:
        return []
    except (json.JSONDecodeError, UnicodeDecodeError) as e:
        print(f"历史文件损坏，创建新的历史记录: {e}")
        # 备份损坏的文件
        import shutil
        import time
        backup_path = f"{file_path}.backup.{int(time.time())}"
        try:
            shutil.copy2(file_path, backup_path)
        except:
            pass
        return []

def save_chat_history(file_path, history):
    """安全保存聊天历史"""
    try:
        # 确保目录存在
        import os
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        # 限制历史记录长度
        if len(history) > MAX_HISTORY_LENGTH:
            history = history[-MAX_HISTORY_LENGTH:]

        # 原子写入：先写入临时文件，再重命名
        temp_path = file_path + '.tmp'
        with open(temp_path, 'w', encoding=ENCODING) as f:
            json.dump(history, f, ensure_ascii=False, indent=2)

        # 原子重命名
        import os
        if os.name == 'nt':  # Windows
            if os.path.exists(file_path):
                os.remove(file_path)
        os.rename(temp_path, file_path)

    except Exception as e:
        print(f"保存历史记录失败: {e}")
        # 清理临时文件
        try:
            os.remove(temp_path)
        except:
            pass

def main():
    if len(sys.argv) < 4:
        print("用法: python unified_ai.py <command_type> <username> <input_value>")
        return
    
    command_type = sys.argv[1]
    username = sys.argv[2]
    input_value = sys.argv[3]
    
    # 检查命令类型是否支持
    if command_type not in MODELS:
        print(f"不支持的命令类型: {command_type}")
        return
    
    model = MODELS[command_type]

    # 确保 userdata 目录存在
    import os
    userdata_dir = 'userdata'
    os.makedirs(userdata_dir, exist_ok=True)

    history_file_path = os.path.join(userdata_dir, f'{username}_chat_history.json')
    
    # 加载聊天历史（改进版）
    chat_history = load_chat_history(history_file_path)
    
    # 添加用户消息
    role = 'user'
    message = {'role': role, 'content': input_value}
    chat_history.append(message)
    
    try:
        if OPENAI_NEW_VERSION:
            # 新版本 OpenAI API (1.0.0+)
            response = client.chat.completions.create(
                model=model,
                messages=chat_history,
                stream=True,
            )

            # 处理流式响应
            completion = {'role': 'assistant', 'content': ''}
            for chunk in response:
                if chunk.choices[0].finish_reason == 'stop':
                    break
                if chunk.choices[0].delta.content:
                    completion['content'] += chunk.choices[0].delta.content
        else:
            # 旧版本 OpenAI API (0.28.x)
            response = openai.ChatCompletion.create(
                model=model,
                messages=chat_history,
                stream=True,
            )

            # 处理流式响应
            completion = {'role': 'assistant', 'content': ''}
            for event in response:
                if event['choices'][0]['finish_reason'] == 'stop':
                    break
                for delta_k, delta_v in event['choices'][0]['delta'].items():
                    completion[delta_k] += delta_v

        # 添加助手回复到历史记录
        chat_history.append(completion)

        # 保存聊天历史（改进版）
        save_chat_history(history_file_path, chat_history)

        # 输出结果
        print(chat_history[-1]['content'])

    except Exception as e:
        print(f"API 调用失败: {str(e)}")

if __name__ == "__main__":
    main()
